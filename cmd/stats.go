package main

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"sort"
	"strconv"
	"time"

	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dialect/mysqldialect"
	_ "github.com/go-sql-driver/mysql"
	"github.com/xuri/excelize/v2"
)

// DailyStats represents daily statistics for a specific page
type DailyStats struct {
	Date           string  `json:"date"`
	Page           string  `json:"page"`
	CreatedOrders  int     `json:"created_orders"`
	PaidOrders     int     `json:"paid_orders"`
	CreatedUsers   int     `json:"created_users"`
	PaidUsers      int     `json:"paid_users"`
	TotalAmount    float64 `json:"total_amount"` // 总支付金额（分转元）
}

// OrderContext represents the order context structure
type OrderContext struct {
	Page   string `json:"page"`
	Source string `json:"source"`
}

// OrderForStats represents order data needed for statistics
type OrderForStats struct {
	bun.BaseModel `bun:"order,alias:o" json:"-"`
	ID            int64        `bun:"id"`
	OrderNo       string       `bun:"order_no"`
	UserID        string       `bun:"user_id"`
	PayStatus     int8         `bun:"pay_status"`
	PayAmount     int          `bun:"pay_amount"`     // 支付金额（分）
	OrderContext  OrderContext `bun:"order_context"`
	CreatedAt     time.Time    `bun:"created_at"`
}

func main() {
	// Database connection string
	dsn := "root_ts:gx_iuOi6uVwgt78ioPlM_we66BnJ@tcp(127.0.0.1:3307)/paipan?charset=utf8mb4&parseTime=True&loc=Local"
	
	// Connect to database
	sqldb, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatal("Failed to open database connection:", err)
	}
	db := bun.NewDB(sqldb, mysqldialect.New())
	defer db.Close()

	// Test database connection
	ctx := context.Background()
	if err := db.PingContext(ctx); err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	fmt.Println("Successfully connected to database")

	// Generate statistics
	stats, err := generateDailyStats(ctx, db)
	if err != nil {
		log.Fatal("Failed to generate statistics:", err)
	}

	// Export to Excel
	err = exportToExcel(stats)
	if err != nil {
		log.Fatal("Failed to export to Excel:", err)
	}

	fmt.Println("Statistics exported to order_stats.xlsx successfully!")
}

// generateDailyStats generates daily statistics grouped by page
func generateDailyStats(ctx context.Context, db *bun.DB) (map[string][]DailyStats, error) {
	// Query orders from the last 30 days
	var orders []OrderForStats
	err := db.NewSelect().
		Model(&orders).
		Where("created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)").
		Order("created_at DESC").
		Scan(ctx)
	
	if err != nil {
		return nil, fmt.Errorf("failed to query orders: %w", err)
	}

	fmt.Printf("Found %d orders in the last 30 days\n", len(orders))

	// Group data by date and page
	dailyData := make(map[string]map[string]*DailyStats)
	
	for _, order := range orders {
		orderDate := order.CreatedAt.Format("2006-01-02")
		page := order.OrderContext.Page
		if page == "" {
			page = "其它"
		}

		// Initialize date map if not exists
		if dailyData[orderDate] == nil {
			dailyData[orderDate] = make(map[string]*DailyStats)
		}

		// Initialize page stats if not exists
		if dailyData[orderDate][page] == nil {
			dailyData[orderDate][page] = &DailyStats{
				Date: orderDate,
				Page: page,
			}
		}

		stats := dailyData[orderDate][page]
		
		// Count created orders
		stats.CreatedOrders++
		
		// Count paid orders and sum payment amount (pay_status = 1 means paid)
		if order.PayStatus == 1 {
			stats.PaidOrders++
			stats.TotalAmount += float64(order.PayAmount) / 100.0 // 分转元
		}
	}

	// Count unique users for each date and page
	err = countUniqueUsers(ctx, db, dailyData)
	if err != nil {
		return nil, fmt.Errorf("failed to count unique users: %w", err)
	}

	// Convert to final format grouped by page
	result := make(map[string][]DailyStats)
	for _, pageStats := range dailyData {
		for page, stats := range pageStats {
			if result[page] == nil {
				result[page] = []DailyStats{}
			}
			result[page] = append(result[page], *stats)
		}
	}

	// Sort each page's stats by date descending
	for page := range result {
		sort.Slice(result[page], func(i, j int) bool {
			return result[page][i].Date > result[page][j].Date
		})
	}

	return result, nil
}

// countUniqueUsers counts unique users for created and paid orders
func countUniqueUsers(ctx context.Context, db *bun.DB, dailyData map[string]map[string]*DailyStats) error {
	for date, pageStats := range dailyData {
		for page, stats := range pageStats {
			// Count unique users who created orders
			var createdUsers int
			query := db.NewSelect().
				Model((*OrderForStats)(nil)).
				ColumnExpr("COUNT(DISTINCT user_id)").
				Where("DATE(created_at) = ?", date)
			
			if page == "其它" {
				query = query.Where("(JSON_EXTRACT(order_context, '$.page') IS NULL OR JSON_EXTRACT(order_context, '$.page') = '')")
			} else {
				query = query.Where("JSON_EXTRACT(order_context, '$.page') = ?", page)
			}
			
			err := query.Scan(ctx, &createdUsers)
			if err != nil {
				return fmt.Errorf("failed to count created users for %s/%s: %w", date, page, err)
			}
			stats.CreatedUsers = createdUsers

			// Count unique users who paid orders
			var paidUsers int
			paidQuery := db.NewSelect().
				Model((*OrderForStats)(nil)).
				ColumnExpr("COUNT(DISTINCT user_id)").
				Where("DATE(created_at) = ?", date).
				Where("pay_status = 1")
			
			if page == "其它" {
				paidQuery = paidQuery.Where("(JSON_EXTRACT(order_context, '$.page') IS NULL OR JSON_EXTRACT(order_context, '$.page') = '')")
			} else {
				paidQuery = paidQuery.Where("JSON_EXTRACT(order_context, '$.page') = ?", page)
			}
			
			err = paidQuery.Scan(ctx, &paidUsers)
			if err != nil {
				return fmt.Errorf("failed to count paid users for %s/%s: %w", date, page, err)
			}
			stats.PaidUsers = paidUsers
		}
	}
	return nil
}

// exportToExcel exports statistics to Excel file
func exportToExcel(stats map[string][]DailyStats) error {
	f := excelize.NewFile()
	defer f.Close()

	// Get all pages and sort them
	var pages []string
	for page := range stats {
		pages = append(pages, page)
	}
	sort.Strings(pages)

	// Get all dates
	dateSet := make(map[string]bool)
	for _, pageStats := range stats {
		for _, stat := range pageStats {
			dateSet[stat.Date] = true
		}
	}

	var dates []string
	for date := range dateSet {
		dates = append(dates, date)
	}
	sort.Sort(sort.Reverse(sort.StringSlice(dates))) // Descending order

	// Create sheets for different metrics
	metrics := []string{"创建订单数", "支付订单数", "创建用户数", "支付用户数", "支付金额"}

	for i, metric := range metrics {
		var sheetName string
		if i == 0 {
			sheetName = "Sheet1"
			f.SetSheetName(sheetName, metric)
		} else {
			sheetName = metric
			_, err := f.NewSheet(sheetName)
			if err != nil {
				return fmt.Errorf("failed to create sheet %s: %w", sheetName, err)
			}
		}

		// Set headers
		f.SetCellValue(sheetName, "A1", "日期")
		for j, page := range pages {
			col := string(rune('B' + j))
			f.SetCellValue(sheetName, col+"1", page)
		}

		// Fill data
		for rowIdx, date := range dates {
			row := rowIdx + 2
			f.SetCellValue(sheetName, "A"+strconv.Itoa(row), date)

			for colIdx, page := range pages {
				col := string(rune('B' + colIdx))

				// Find stats for this date and page
				var value interface{} = 0
				if pageStats, exists := stats[page]; exists {
					for _, stat := range pageStats {
						if stat.Date == date {
							switch metric {
							case "创建订单数":
								value = stat.CreatedOrders
							case "支付订单数":
								value = stat.PaidOrders
							case "创建用户数":
								value = stat.CreatedUsers
							case "支付用户数":
								value = stat.PaidUsers
							case "支付金额":
								value = stat.TotalAmount
							}
							break
						}
					}
				}

				f.SetCellValue(sheetName, col+strconv.Itoa(row), value)
			}
		}
	}

	// Save the file
	if err := f.SaveAs("order_stats.xlsx"); err != nil {
		return fmt.Errorf("failed to save Excel file: %w", err)
	}

	return nil
}

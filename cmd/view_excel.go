package main

import (
	"fmt"
	"log"

	"github.com/xuri/excelize/v2"
)

func main() {
	// Open the Excel file
	f, err := excelize.OpenFile("order_stats.xlsx")
	if err != nil {
		log.Fatal("Failed to open Excel file:", err)
	}
	defer f.Close()

	// Get all sheet names
	sheets := f.GetSheetList()
	fmt.Printf("Excel文件包含 %d 个工作表:\n", len(sheets))
	for i, sheet := range sheets {
		fmt.Printf("%d. %s\n", i+1, sheet)
	}

	// Show sample data from the first sheet
	fmt.Printf("\n=== %s 工作表示例数据 ===\n", sheets[0])
	
	// Get the used range
	rows, err := f.GetRows(sheets[0])
	if err != nil {
		log.Fatal("Failed to get rows:", err)
	}

	// Print first 10 rows
	maxRows := 10
	if len(rows) < maxRows {
		maxRows = len(rows)
	}

	for i := 0; i < maxRows; i++ {
		if i < len(rows) {
			row := rows[i]
			// Print first 8 columns
			maxCols := 8
			if len(row) < maxCols {
				maxCols = len(row)
			}
			
			for j := 0; j < maxCols; j++ {
				if j < len(row) {
					fmt.Printf("%-12s", row[j])
				} else {
					fmt.Printf("%-12s", "")
				}
			}
			if len(row) > maxCols {
				fmt.Printf("...")
			}
			fmt.Println()
		}
	}

	if len(rows) > maxRows {
		fmt.Printf("... (还有 %d 行数据)\n", len(rows)-maxRows)
	}

	fmt.Printf("\n总共 %d 行数据\n", len(rows))
}

package main

import (
	"bufio"
	"encoding/json"
	"fmt"
	"log"
	"os"
)

// LogEntry represents the outer structure of each log line
type LogEntry struct {
	Log string `json:"__log__"`
}

// InnerLog represents the parsed inner log structure
type InnerLog struct {
	Level      string      `json:"level"`
	Ts         string      `json:"ts"`
	Caller     string      `json:"caller"`
	Msg        string      `json:"msg"`
	TraceID    string      `json:"trace_id"`
	RequestURL string      `json:"request_url"`
	RequestParams interface{} `json:"request_params,omitempty"`
	ResponseBody  interface{} `json:"response_body,omitempty"`
}

// RequestParams represents the request parameters structure
type RequestParams struct {
	OrderSource OrderSource `json:"order_source"`
	SkuCode     string      `json:"skuCode"`
}

// OrderSource represents the order source structure
type OrderSource struct {
	Page   string `json:"page"`
	Source string `json:"source"`
}

// ResponseBody represents the response body structure
type ResponseBody struct {
	Code    int         `json:"code"`
	Data    ResponseData `json:"data"`
	Message string      `json:"message"`
}

// ResponseData represents the data field in response
type ResponseData struct {
	OrderNo string `json:"orderNo"`
}

// OrderRecord represents the final output structure
type OrderRecord struct {
	TraceID     string      `json:"trace_id"`
	OrderSource OrderSource `json:"order_source"`
	SkuCode     string      `json:"sku_code"`
	Ts          string      `json:"ts"`
	OrderNo     string      `json:"order_no"`
}

func main() {
	// Open the input file
	file, err := os.Open("order.json")
	if err != nil {
		log.Fatal("Error opening file:", err)
	}
	defer file.Close()

	// Maps to store requests and responses by trace_id
	requests := make(map[string]*InnerLog)
	responses := make(map[string]*InnerLog)

	scanner := bufio.NewScanner(file)
	lineCount := 0

	// Read and parse each line
	for scanner.Scan() {
		lineCount++
		line := scanner.Text()

		var logEntry LogEntry
		if err := json.Unmarshal([]byte(line), &logEntry); err != nil {
			fmt.Printf("Error parsing outer JSON on line %d: %v\n", lineCount, err)
			continue
		}

		var innerLog InnerLog
		if err := json.Unmarshal([]byte(logEntry.Log), &innerLog); err != nil {
			fmt.Printf("Error parsing inner JSON on line %d: %v\n", lineCount, err)
			continue
		}

		// Only process records with request_url = "/v1/order/create"
		if innerLog.RequestURL != "/v1/order/create" {
			continue
		}

		// Store requests and responses separately
		if innerLog.Msg == "Request" {
			requests[innerLog.TraceID] = &innerLog
		} else if innerLog.Msg == "Response" {
			responses[innerLog.TraceID] = &innerLog
		}
	}

	if err := scanner.Err(); err != nil {
		log.Fatal("Error reading file:", err)
	}

	// Process matched pairs and create output records
	var orderRecords []OrderRecord

	for traceID, request := range requests {
		response, exists := responses[traceID]
		if !exists {
			fmt.Printf("Warning: No response found for trace_id: %s\n", traceID)
			continue
		}

		// Parse request parameters
		var requestParams RequestParams
		if request.RequestParams != nil {
			requestParamsBytes, err := json.Marshal(request.RequestParams)
			if err != nil {
				fmt.Printf("Error marshaling request params for trace_id %s: %v\n", traceID, err)
				continue
			}
			if err := json.Unmarshal(requestParamsBytes, &requestParams); err != nil {
				fmt.Printf("Error parsing request params for trace_id %s: %v\n", traceID, err)
				continue
			}
		}

		// Parse response body
		var responseBody ResponseBody
		if response.ResponseBody != nil {
			responseBodyBytes, err := json.Marshal(response.ResponseBody)
			if err != nil {
				fmt.Printf("Error marshaling response body for trace_id %s: %v\n", traceID, err)
				continue
			}
			if err := json.Unmarshal(responseBodyBytes, &responseBody); err != nil {
				fmt.Printf("Error parsing response body for trace_id %s: %v\n", traceID, err)
				continue
			}
		}

		// Create order record
		orderRecord := OrderRecord{
			TraceID:     traceID,
			OrderSource: requestParams.OrderSource,
			SkuCode:     requestParams.SkuCode,
			Ts:          response.Ts,
			OrderNo:     responseBody.Data.OrderNo,
		}

		orderRecords = append(orderRecords, orderRecord)
	}

	// Output as JSON
	outputBytes, err := json.MarshalIndent(orderRecords, "", "  ")
	if err != nil {
		log.Fatal("Error marshaling output:", err)
	}

	// Write to output file
	outputFile, err := os.Create("processed_orders.json")
	if err != nil {
		log.Fatal("Error creating output file:", err)
	}
	defer outputFile.Close()

	if _, err := outputFile.Write(outputBytes); err != nil {
		log.Fatal("Error writing to output file:", err)
	}

	fmt.Printf("Processing complete!\n")
	fmt.Printf("Processed %d lines\n", lineCount)
	fmt.Printf("Found %d requests and %d responses for /v1/order/create\n", len(requests), len(responses))
	fmt.Printf("Created %d order records\n", len(orderRecords))
	fmt.Printf("Output written to processed_orders.json\n")
}

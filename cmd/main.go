package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dialect/mysqldialect"
)

type Product struct {
	bun.BaseModel         `bun:"product,alias:p" json:"-"`
	ID                    int64  `bun:"id,pk" json:"id"`
	SkuCode               string `bun:"sku_code" json:"sku_code"`                             // SKU编码
	IsSubscription        bool   `bun:"is_subscription" json:"isSubscription"`                // 是否订阅商品
	SubscriptionCycleDays int    `bun:"subscription_cycle_days" json:"subscriptionCycleDays"` // 订阅周期天数
	Sort                  int    `bun:"sort" json:"sort"`                                     // 排序
	ProductCreate
	BaseFields4Time
}

type BaseFields4Time struct {
	CreatedAt time.Time `bun:",nullzero,notnull,default:current_timestamp"` // 创建时间
	UpdatedAt time.Time `bun:",nullzero,notnull,default:current_timestamp"` // 更新时间
}

func (slf *BaseFields4Time) BeforeAppendModel(_ context.Context, query bun.Query) error {
	switch q := query.(type) {
	case *bun.InsertQuery:
		slf.CreatedAt = time.Now()
		slf.UpdatedAt = time.Now()
	case *bun.UpdateQuery:
		slf.UpdatedAt = time.Now()
		if !strings.Contains(q.String(), "updated_at") {
			q.Set("updated_at = ?", time.Now())
		}
	}
	return nil
}

type ProductCreate struct {
	SkuName       string `bun:"sku_name" json:"sku_name" required:"true"`            // SKU名称
	Specification string `bun:"specification" json:"specification" required:"true"`  // 规格
	OriginalPrice int    `bun:"original_price" json:"originalPrice" required:"true"` // 划线价
	SalePrice     int    `bun:"sale_price" json:"salePrice" required:"true"`         // 出售价
	Stock         int64  `bun:"stock" json:"stock" required:"true"`                  // 库存量
	Type          int64  `bun:"type" json:"type" required:"true"`                    // 商品类型：0-其他，1-虚拟会员商品，2-论财咨询服务，3-MLBZ，4-配饰
	Application   string `bun:"application" json:"application" required:"true"`      // 所属应用
	Remark        string `bun:"remark" json:"remark"`                                // 备注
	Enable        int64  `bun:"enable" json:"enable"`                                // 是否启用：0-禁用，1-启用
	ValidityDays  int64  `bun:"validity_days" json:"validityDays" required:"true"`   // 会员有效期天数
}

type Order struct {
	bun.BaseModel   `bun:"order,alias:o" json:"-"`
	ID              int64          `bun:"id,pk" json:"id"`
	AppID           int64          `bun:"app_id" json:"app_id"`
	OrderNo         string         `bun:"order_no" json:"orderNo"`                 // 订单号
	OrderUUID       string         `bun:"order_uuid" json:"orderUuid"`             // 订单UUID
	UserID          string         `bun:"user_id" json:"userId"`                   // 用户ID
	ProductID       int64          `bun:"product_id" json:"productId"`             // 商品ID
	ProductSnapshot *Product       `bun:"product_snapshot" json:"productSnapshot"` // 商品快照
	Quantity        int64          `bun:"quantity" json:"quantity"`                // 购买数量
	Amount          int            `bun:"amount" json:"amount"`                    // 订单金额
	PayAmount       int            `bun:"pay_amount" json:"payAmount"`             // 支付金额
	PayChannel      int8           `bun:"pay_channel" json:"payChannel"`           // 支付渠道：1-微信支付，2-支付宝
	PayStatus       int8           `bun:"pay_status" json:"payStatus"`             // 支付状态：0-待支付，1-支付成功，2-支付失败，3-已退款（全部）、4-已退款（部分）
	TransactionID   string         `bun:"transaction_id" json:"transactionId"`     // 支付平台交易号
	PayTime         *time.Time     `bun:"pay_time" json:"payTime"`                 // 支付时间
	RefundAmount    int            `bun:"refund_amount" json:"refundAmount"`       // 退款金额
	RefundTime      *time.Time     `bun:"refund_time" json:"refundTime"`           // 退款时间
	ExpireTime      time.Time      `bun:"expire_time" json:"expireTime"`           // 订单过期时间
	Status          int8           `bun:"status" json:"status"`                    // 订单状态：0-待支付，1-已完成，2-已取消，3-已退款
	Remark          string         `bun:"remark" json:"remark"`                    // 备注
	ExtraInfo       map[string]any `bun:"extra_info" json:"extraInfo"`             // 额外信息
	UA              string         `bun:"ua" json:"ua"`                            // 用户UA
	IP              string         `bun:"ip" json:"ip"`                            // 用户IP
	DeviceID        string         `bun:"device_id" json:"device_id"`              // 设备ID
	Source          int            `bun:"source" json:"source"`                    // 订单来源：0-未知，1-安卓应用，2-苹果应用，3-安卓h5，4-苹果h5、5-自动续费
	OrderContext    OrderContext   `bun:"order_context" json:"order_context"`      // 订单上下文
	BaseFields4Time
}

type OrderContext struct {
	Page   string `json:"page"`
	Source string `json:"source"`
}

// ProcessedOrder represents the structure from processed_orders.json
type ProcessedOrder struct {
	TraceID      string       `json:"trace_id"`
	OrderContext OrderContext `json:"order_context"`
	SkuCode      string       `json:"sku_code"`
	Ts           string       `json:"ts"`
	OrderNo      string       `json:"order_no"`
}

func main() {
	// Database connection string
	dsn := "root_ts:gx_iuOi6uVwgt78ioPlM_we66BnJ@tcp(127.0.0.1:3307)/paipan?charset=utf8mb4&parseTime=True&loc=Local"

	// Connect to database
	sqldb, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatal("Failed to open database connection:", err)
	}
	db := bun.NewDB(sqldb, mysqldialect.New())
	defer db.Close()

	// Test database connection
	ctx := context.Background()
	if err := db.PingContext(ctx); err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	fmt.Println("Successfully connected to database")

	// Read processed orders from JSON file
	processedOrders, err := readProcessedOrders("./processed_orders.json")
	if err != nil {
		log.Fatal("Failed to read processed orders:", err)
	}
	fmt.Printf("Loaded %d processed orders\n", len(processedOrders))

	// Update orders in database
	updateCount, err := updateOrdersContext(ctx, db, processedOrders)
	if err != nil {
		log.Fatal("Failed to update orders:", err)
	}

	fmt.Printf("Successfully updated %d orders\n", updateCount)
}

// readProcessedOrders reads and parses the processed_orders.json file
func readProcessedOrders(filename string) ([]ProcessedOrder, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	var orders []ProcessedOrder
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&orders); err != nil {
		return nil, fmt.Errorf("failed to decode JSON: %w", err)
	}

	return orders, nil
}

// updateOrdersContext updates the order_context field for orders in the database
func updateOrdersContext(ctx context.Context, db *bun.DB, processedOrders []ProcessedOrder) (int, error) {
	updateCount := 0
	notFoundCount := 0

	for i, processedOrder := range processedOrders {
		fmt.Println("Processing order:", processedOrder)
		// First, query the order to check if it exists and get current order_context
		var existingOrder Order
		err := db.NewSelect().
			Model(&existingOrder).
			Where("order_no = ?", processedOrder.OrderNo).
			Scan(ctx)

		if err != nil {
			if err == sql.ErrNoRows {
				fmt.Printf("Order not found: %s\n", processedOrder.OrderNo)
				continue
			}
			fmt.Printf("Error querying order %s: %v\n", processedOrder.OrderNo, err)
			continue
		}

		// Show current and new order context for first few orders
		if i < 5 {
			fmt.Printf("Order %s:\n", processedOrder.OrderNo)
			fmt.Printf("  Current order_context: %+v\n", existingOrder.OrderContext)
			fmt.Printf("  New order_context: %+v\n", processedOrder.OrderContext)
			fmt.Printf("  ---\n")
		}

		// Update order by order_no
		result, err := db.NewUpdate().
			Model((*Order)(nil)).
			Set("order_context = ?", processedOrder.OrderContext).
			Where("order_no = ?", processedOrder.OrderNo).
			Exec(ctx)

		if err != nil {
			fmt.Printf("Error updating order %s: %v\n", processedOrder.OrderNo, err)
			continue
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			fmt.Printf("Error getting rows affected for order %s: %v\n", processedOrder.OrderNo, err)
			continue
		}

		if rowsAffected > 0 {
			updateCount++
			if (i+1)%100 == 0 {
				fmt.Printf("Processed %d/%d orders... (Updated: %d, Not found: %d)\n",
					i+1, len(processedOrders), updateCount, notFoundCount)
			}
		}
	}

	fmt.Printf("Final summary: Updated: %d, Not found: %d\n", updateCount, notFoundCount)
	return updateCount, nil
}
